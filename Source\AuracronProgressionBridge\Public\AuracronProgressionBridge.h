﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de ProgressÃ£o Bridge
// IntegraÃ§Ã£o C++ para progressÃ£o usando Firebase e Epic Online Services com APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/GameFrameworkComponent.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineUserCloudInterface.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "AuracronProgressionBridge.generated.h"

// Forward Declarations
class IOnlineSubsystem;
class IOnlineAchievementsInterface;
class IOnlineLeaderboardInterface;
class IOnlineStatsInterface;
class IOnlineUserCloudInterface;

/**
 * EnumeraÃ§Ã£o para tipos de progressÃ£o
 */
UENUM(BlueprintType)
enum class EAuracronProgressionType : uint8
{
    None                UMETA(DisplayName = "None"),
    AccountLevel        UMETA(DisplayName = "Account Level"),
    ChampionMastery     UMETA(DisplayName = "Champion Mastery"),
    RealmMastery        UMETA(DisplayName = "Realm Mastery"),
    SeasonalRank        UMETA(DisplayName = "Seasonal Rank"),
    BattlePass          UMETA(DisplayName = "Battle Pass"),
    Achievement         UMETA(DisplayName = "Achievement"),
    Collection          UMETA(DisplayName = "Collection"),
    Milestone           UMETA(DisplayName = "Milestone")
};

/**
 * EnumeraÃ§Ã£o para tipos de recompensa
 */
UENUM(BlueprintType)
enum class EAuracronRewardType : uint8
{
    None                UMETA(DisplayName = "None"),
    Experience          UMETA(DisplayName = "Experience"),
    Gold                UMETA(DisplayName = "Gold"),
    PremiumCurrency     UMETA(DisplayName = "Premium Currency"),
    Champion            UMETA(DisplayName = "Champion"),
    Skin                UMETA(DisplayName = "Skin"),
    Emote               UMETA(DisplayName = "Emote"),
    Icon                UMETA(DisplayName = "Icon"),
    Border              UMETA(DisplayName = "Border"),
    Title               UMETA(DisplayName = "Title"),
    Ward                UMETA(DisplayName = "Ward"),
    Recall              UMETA(DisplayName = "Recall"),
    Sigilo              UMETA(DisplayName = "Sigilo"),
    Boost               UMETA(DisplayName = "Boost"),
    Chest               UMETA(DisplayName = "Chest"),
    Key                 UMETA(DisplayName = "Key"),
    Essence             UMETA(DisplayName = "Essence")
};

/**
 * EnumeraÃ§Ã£o para raridade de recompensas
 */
UENUM(BlueprintType)
enum class EAuracronRewardRarity : uint8
{
    Common          UMETA(DisplayName = "Common"),
    Uncommon        UMETA(DisplayName = "Uncommon"),
    Rare            UMETA(DisplayName = "Rare"),
    Epic            UMETA(DisplayName = "Epic"),
    Legendary       UMETA(DisplayName = "Legendary"),
    Mythic          UMETA(DisplayName = "Mythic"),
    Ultimate        UMETA(DisplayName = "Ultimate")
};

/**
 * Estrutura para recompensa
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronReward
{
    GENERATED_BODY()

    /** ID Ãºnico da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FString RewardID;

    /** Nome da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FText RewardName;

    /** DescriÃ§Ã£o da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FText RewardDescription;

    /** Tipo da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    EAuracronRewardType RewardType = EAuracronRewardType::None;

    /** Raridade da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    EAuracronRewardRarity RewardRarity = EAuracronRewardRarity::Common;

    /** Quantidade da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward", meta = (ClampMin = "1", ClampMax = "1000000"))
    int32 Quantity = 1;

    /** Ãcone da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    TSoftObjectPtr<UTexture2D> RewardIcon;

    /** Valor em moeda premium */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward", meta = (ClampMin = "0", ClampMax = "10000"))
    int32 PremiumValue = 0;

    /** Valor em gold */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward", meta = (ClampMin = "0", ClampMax = "100000"))
    int32 GoldValue = 0;

    /** Ã‰ recompensa premium */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    bool bIsPremiumReward = false;

    /** Requer Battle Pass */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    bool bRequiresBattlePass = false;

    /** Data de expiraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FDateTime ExpirationDate;

    /** Tem data de expiraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    bool bHasExpirationDate = false;

    /** Tags da recompensa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    FGameplayTagContainer RewardTags;
};

/**
 * Estrutura para progressÃ£o de conta
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronAccountProgression
{
    GENERATED_BODY()

    /** NÃ­vel da conta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "1", ClampMax = "500"))
    int32 AccountLevel = 1;

    /** ExperiÃªncia atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int64 CurrentExperience = 0;

    /** ExperiÃªncia total acumulada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int64 TotalExperience = 0;

    /** ExperiÃªncia para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "100"))
    int64 ExperienceToNextLevel = 1000;

    /** Gold atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 CurrentGold = 0;

    /** Moeda premium atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 PremiumCurrency = 0;

    /** EssÃªncia azul atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 BlueEssence = 0;

    /** EssÃªncia laranja atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 OrangeEssence = 0;

    /** Partidas jogadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 MatchesPlayed = 0;

    /** Partidas vencidas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 MatchesWon = 0;

    /** Taxa de vitÃ³ria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float WinRate = 0.0f;

    /** Tempo total jogado (em minutos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 TotalPlayTimeMinutes = 0;

    /** Data de criaÃ§Ã£o da conta */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression")
    FDateTime AccountCreationDate;

    /** Ãšltima vez online */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression")
    FDateTime LastOnlineDate;

    /** Streak de vitÃ³rias atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 CurrentWinStreak = 0;

    /** Maior streak de vitÃ³rias */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 BestWinStreak = 0;

    /** Honra atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0", ClampMax = "5"))
    int32 HonorLevel = 2;

    /** Pontos de honra */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 HonorPoints = 0;

    /** Tem Battle Pass ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression")
    bool bHasActiveBattlePass = false;

    /** NÃ­vel do Battle Pass */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0", ClampMax = "100"))
    int32 BattlePassLevel = 0;

    /** XP do Battle Pass */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Account Progression", meta = (ClampMin = "0"))
    int32 BattlePassXP = 0;
};

/**
 * Estrutura para maestria de campeÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronChampionMastery
{
    GENERATED_BODY()

    /** ID do campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    FString ChampionID;

    /** NÃ­vel de maestria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MasteryLevel = 0;

    /** Pontos de maestria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 MasteryPoints = 0;

    /** Pontos para prÃ³ximo nÃ­vel */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 PointsToNextLevel = 1800;

    /** Partidas jogadas com o campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 MatchesPlayed = 0;

    /** Partidas vencidas com o campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 MatchesWon = 0;

    /** Taxa de vitÃ³ria com o campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ChampionWinRate = 0.0f;

    /** KDA mÃ©dio com o campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0.0"))
    float AverageKDA = 0.0f;

    /** Melhor KDA com o campeÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0.0"))
    float BestKDA = 0.0f;

    /** Tempo total jogado com o campeÃ£o (em minutos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 TotalPlayTime = 0;

    /** Ãšltima vez jogado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    FDateTime LastPlayed;

    /** Marcos desbloqueados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    TArray<FString> UnlockedMilestones;

    /** Recompensas disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    TArray<FAuracronReward> AvailableRewards;

    /** Tem token de maestria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    bool bHasMasteryToken = false;

    /** NÃºmero de tokens de maestria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery", meta = (ClampMin = "0"))
    int32 MasteryTokens = 0;
};

/**
 * Estrutura para maestria de realm
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronRealmMastery
{
    GENERATED_BODY()

    /** Tipo do realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    int32 RealmType = 0; // 0=PlanÃ­cie, 1=Firmamento, 2=Abismo

    /** NÃ­vel de maestria do realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MasteryLevel = 0;

    /** Pontos de maestria do realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 MasteryPoints = 0;

    /** Tempo passado no realm (em minutos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 TimeSpentInRealm = 0;

    /** Objetivos completados no realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 ObjectivesCompleted = 0;

    /** Kills no realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 KillsInRealm = 0;

    /** Mortes no realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 DeathsInRealm = 0;

    /** AssistÃªncias no realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0"))
    int32 AssistsInRealm = 0;

    /** Segredos descobertos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    TArray<FString> DiscoveredSecrets;

    /** Ãreas exploradas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ExplorationPercentage = 0.0f;

    /** Marcos de realm desbloqueados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    TArray<FString> UnlockedRealmMilestones;

    /** Recompensas de realm disponÃ­veis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    TArray<FAuracronReward> RealmRewards;
};

/**
 * Estrutura para marco de progressÃ£o
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronProgressionMilestone
{
    GENERATED_BODY()

    /** ID Ãºnico do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    FString MilestoneID;

    /** Nome do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    FText MilestoneName;

    /** DescriÃ§Ã£o do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    FText MilestoneDescription;

    /** Tipo de progressÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    EAuracronProgressionType ProgressionType = EAuracronProgressionType::None;

    /** Valor necessÃ¡rio para completar */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone", meta = (ClampMin = "1"))
    int32 RequiredValue = 100;

    /** Valor atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone", meta = (ClampMin = "0"))
    int32 CurrentValue = 0;

    /** Marco foi completado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    bool bCompleted = false;

    /** Data de conclusÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    FDateTime CompletionDate;

    /** Recompensas do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    TArray<FAuracronReward> MilestoneRewards;

    /** Ãcone do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    TSoftObjectPtr<UTexture2D> MilestoneIcon;

    /** Ã‰ marco secreto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    bool bIsSecretMilestone = false;

    /** Ã‰ marco sazonal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    bool bIsSeasonalMilestone = false;

    /** Temporada do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone", meta = (ClampMin = "1"))
    int32 Season = 1;

    /** Prioridade de exibiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone", meta = (ClampMin = "0", ClampMax = "10"))
    int32 DisplayPriority = 5;

    /** Tags do marco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression Milestone")
    FGameplayTagContainer MilestoneTags;
};

/**
 * Estrutura para entrada de maestria de campeão (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronChampionMasteryEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    FString ChampionID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Champion Mastery")
    FAuracronChampionMastery Mastery;

    FAuracronChampionMasteryEntry()
    {
        ChampionID = TEXT("");
    }

    FAuracronChampionMasteryEntry(const FString& InChampionID, const FAuracronChampionMastery& InMastery)
        : ChampionID(InChampionID), Mastery(InMastery)
    {
    }
};

/**
 * Estrutura para entrada de maestria de realm (substitui TMap para replicação)
 */
USTRUCT(BlueprintType)
struct AURACRONPROGRESSIONBRIDGE_API FAuracronRealmMasteryEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    int32 RealmID = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Mastery")
    FAuracronRealmMastery Mastery;

    FAuracronRealmMasteryEntry()
    {
        RealmID = 0;
    }

    FAuracronRealmMasteryEntry(int32 InRealmID, const FAuracronRealmMastery& InMastery)
        : RealmID(InRealmID), Mastery(InMastery)
    {
    }
};

/**
 * Classe principal do Bridge para Sistema de ProgressÃ£o
 * ResponsÃ¡vel pelo gerenciamento completo de progressÃ£o com Firebase e EOS
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Progression", meta = (DisplayName = "AURACRON Progression Bridge", BlueprintSpawnableComponent))
class AURACRONPROGRESSIONBRIDGE_API UAuracronProgressionBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronProgressionBridge(const FObjectInitializer& ObjectInitializer);

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Account Progression ===

    /**
     * Ganhar experiÃªncia de conta
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    bool GainAccountExperience(int32 ExperienceAmount);

    /**
     * Subir nÃ­vel de conta
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    bool LevelUpAccount();

    /**
     * Adicionar gold
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    bool AddGold(int32 GoldAmount);

    /**
     * Remover gold
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    bool RemoveGold(int32 GoldAmount);

    /**
     * Adicionar moeda premium
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    bool AddPremiumCurrency(int32 Amount);

    /**
     * Obter progressÃ£o da conta
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    FAuracronAccountProgression GetAccountProgression() const { return CurrentAccountProgression; }

    /**
     * Calcular experiÃªncia necessÃ¡ria para nÃ­vel
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Account", CallInEditor)
    int64 CalculateExperienceForLevel(int32 Level) const;

    // === Champion Mastery ===

    /**
     * Ganhar pontos de maestria de campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Champion", CallInEditor)
    bool GainChampionMasteryPoints(const FString& ChampionID, int32 Points);

    /**
     * Obter maestria de campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Champion", CallInEditor)
    FAuracronChampionMastery GetChampionMastery(const FString& ChampionID) const;

    /**
     * Obter todas as maestrias de campeÃµes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Champion", CallInEditor)
    TArray<FAuracronChampionMastery> GetAllChampionMasteries() const;

    /**
     * Subir nÃ­vel de maestria de campeÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Champion", CallInEditor)
    bool LevelUpChampionMastery(const FString& ChampionID);

    // === Realm Mastery ===

    /**
     * Ganhar pontos de maestria de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Realm", CallInEditor)
    bool GainRealmMasteryPoints(int32 RealmType, int32 Points);

    /**
     * Obter maestria de realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Realm", CallInEditor)
    FAuracronRealmMastery GetRealmMastery(int32 RealmType) const;

    /**
     * Registrar tempo no realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Realm", CallInEditor)
    bool RegisterTimeInRealm(int32 RealmType, int32 TimeMinutes);

    /**
     * Descobrir segredo do realm
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Realm", CallInEditor)
    bool DiscoverRealmSecret(int32 RealmType, const FString& SecretID);

    // === Milestones and Achievements ===

    /**
     * Completar marco de progressÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Milestones", CallInEditor)
    bool CompleteMilestone(const FString& MilestoneID);

    /**
     * Obter marcos disponÃ­veis
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Milestones", CallInEditor)
    TArray<FAuracronProgressionMilestone> GetAvailableMilestones() const;

    /**
     * Obter marcos completados
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Milestones", CallInEditor)
    TArray<FAuracronProgressionMilestone> GetCompletedMilestones() const;

    /**
     * Verificar progresso de marco
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Milestones", CallInEditor)
    bool CheckMilestoneProgress(const FString& MilestoneID, int32 NewValue);

    // === Rewards Management ===

    /**
     * Conceder recompensa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Rewards", CallInEditor)
    bool GrantReward(const FAuracronReward& Reward);

    /**
     * Obter recompensas pendentes
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Rewards", CallInEditor)
    TArray<FAuracronReward> GetPendingRewards() const;

    /**
     * Coletar recompensa
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Rewards", CallInEditor)
    bool ClaimReward(const FString& RewardID);

    /**
     * Coletar todas as recompensas
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Rewards", CallInEditor)
    bool ClaimAllRewards();

    // === Cloud Save Management ===

    /**
     * Salvar progressÃ£o na nuvem
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Cloud", CallInEditor)
    bool SaveProgressionToCloud();

    /**
     * Carregar progressÃ£o da nuvem
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Cloud", CallInEditor)
    bool LoadProgressionFromCloud();

    /**
     * Sincronizar com Firebase
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Cloud", CallInEditor)
    bool SyncWithFirebase();

    /**
     * Fazer backup da progressÃ£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Progression|Cloud", CallInEditor)
    bool BackupProgression();

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de progressÃ£o */
    bool InitializeProgressionSystem();

    /** Configurar Firebase */
    bool SetupFirebase();

    /** Configurar Epic Online Services */
    bool SetupEpicOnlineServices();

    /** Processar progressÃ£o automÃ¡tica */
    void ProcessAutomaticProgression(float DeltaTime);

    /** Validar recompensa */
    bool ValidateReward(const FAuracronReward& Reward) const;

    /** Calcular pontos de maestria */
    int32 CalculateMasteryPoints(EAuracronProgressionType Type, int32 BaseValue) const;

    /** Verificar marcos automaticamente */
    void CheckAutomaticMilestones();

    /** Serializar dados de progressÃ£o */
    FString SerializeProgressionData() const;

    /** Deserializar dados de progressÃ£o */
    bool DeserializeProgressionData(const FString& JsonData);

    // === Firebase Callbacks ===

    /** Callback para save no Firebase */
    void OnFirebaseSaveComplete(bool bSuccess, const FString& ErrorMessage);

    /** Callback para load do Firebase */
    void OnFirebaseLoadComplete(bool bSuccess, const FString& Data, const FString& ErrorMessage);

public:
    // === Configuration Properties ===

    /** ProgressÃ£o da conta atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    FAuracronAccountProgression CurrentAccountProgression;

    /** Maestrias de campeÃµes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronChampionMasteryEntry> ChampionMasteries;

    /** Maestrias de realms */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", Replicated)
    TArray<FAuracronRealmMasteryEntry> RealmMasteries;

    /** Marcos de progressÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FAuracronProgressionMilestone> ProgressionMilestones;

    /** Recompensas pendentes */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_PendingRewards)
    TArray<FAuracronReward> PendingRewards;

    /** Ãšltima sincronizaÃ§Ã£o com a nuvem */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    FDateTime LastCloudSync;

private:
    // === Internal State ===

    /** Online Subsystem Interface */
    IOnlineSubsystem* OnlineSubsystem = nullptr;

    /** Achievements Interface */
    TSharedPtr<IOnlineAchievementsInterface> AchievementsInterface;

    /** Stats Interface */
    TSharedPtr<IOnlineStatsInterface> StatsInterface;

    /** Cloud Interface */
    TSharedPtr<IOnlineUserCloudInterface> CloudInterface;

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** Firebase inicializado */
    bool bFirebaseInitialized = false;

    /** Timer para sincronizaÃ§Ã£o automÃ¡tica */
    FTimerHandle AutoSyncTimer;

    /** Timer para verificaÃ§Ã£o de marcos */
    FTimerHandle MilestoneCheckTimer;

    /** Mutex para thread safety */
    mutable FCriticalSection ProgressionMutex;

    /** Cache de dados para otimizaÃ§Ã£o */
    TMap<FString, FString> ProgressionCache;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_PendingRewards();

public:
    // === Delegates ===

    /** Delegate chamado quando conta sobe de nÃ­vel */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAccountLevelUp, int32, OldLevel, int32, NewLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Progression|Events")
    FOnAccountLevelUp OnAccountLevelUp;

    /** Delegate chamado quando maestria de campeÃ£o sobe de nÃ­vel */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnChampionMasteryLevelUp, FString, ChampionID, int32, OldLevel, int32, NewLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Progression|Events")
    FOnChampionMasteryLevelUp OnChampionMasteryLevelUp;

    /** Delegate chamado quando marco Ã© completado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMilestoneCompleted, FString, MilestoneID, TArray<FAuracronReward>, Rewards);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Progression|Events")
    FOnMilestoneCompleted OnMilestoneCompleted;

    /** Delegate chamado quando recompensa Ã© concedida */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRewardGranted, FAuracronReward, Reward);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Progression|Events")
    FOnRewardGranted OnRewardGranted;

    /** Delegate chamado quando progressÃ£o Ã© sincronizada */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnProgressionSynced, bool, bSuccess);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Progression|Events")
    FOnProgressionSynced OnProgressionSynced;
};

